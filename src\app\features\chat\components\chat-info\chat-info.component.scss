:host {
  display: block;
  width: 360px; // Increased width for better content display
  border-left: 1px solid #e4e6ea;
  background-color: #ffffff;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.04); // Subtle shadow for depth
}

.chat-info-container {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #c4c4c4;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// User Profile Section
.user-profile {
  text-align: center;
  padding: 2rem 1.5rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f2f5;
  flex-shrink: 0;
}

.profile-avatar {
  width: 100px; // Larger avatar for better visual impact
  height: 100px;
  border-radius: 50%;
  margin-bottom: 1rem;
  object-fit: cover;
  border: 4px solid #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.nickname-section {
  margin-bottom: 0.5rem;
}

.nickname-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.profile-name {
  font-size: 1.75rem; // Larger name for better hierarchy
  font-weight: 700; // Bolder weight
  color: #1c1e21; // Darker color for better contrast
  margin: 0;
  letter-spacing: -0.02em; // Tighter letter spacing
}

.edit-nickname-btn {
  background: none;
  border: none;
  color: #65676b;
  cursor: pointer;
  padding: 0.375rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #1c1e21;
    background-color: #f0f2f5;
    transform: scale(1.05);
  }
}

.nickname-edit {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.nickname-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  text-align: center;
  width: 200px;

  &:focus {
    outline: none;
    border-color: var(--chat-theme-color, #7b42f6);
    box-shadow: 0 0 0 3px rgba(123, 66, 246, 0.1);
  }
}

.nickname-actions {
  display: flex;
  gap: 0.5rem;
}

.save-btn,
.cancel-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn {
  background-color: #10b981;
  color: white;

  &:hover {
    background-color: #059669;
  }
}

.cancel-btn {
  background-color: #ef4444;
  color: white;

  &:hover {
    background-color: #dc2626;
  }
}

.profile-status {
  color: #65676b;
  font-size: 0.95rem;
  margin: 0;
  font-weight: 500;

  &.active {
    color: #42b883;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &::before {
      content: "";
      width: 8px;
      height: 8px;
      background-color: #42b883;
      border-radius: 50%;
      display: inline-block;
    }
  }
}

// Tab Navigation
.tab-navigation {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin: 1rem 1.5rem;
  padding: 4px;
  flex-shrink: 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  padding: 0.875rem 0.5rem;
  border: none;
  background: none;
  color: #65676b;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.375rem;
  transition: all 0.2s ease;
  border-radius: 8px;
  position: relative;

  &:hover {
    color: #1c1e21;
    background-color: rgba(255, 255, 255, 0.5);
  }

  &.active {
    color: var(--chat-theme-color, #1877f2);
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  i {
    font-size: 1.1rem;
  }

  span {
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

// Tab Content
.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 1.5rem 1.5rem;
}

.tab-panel {
  animation: fadeIn 0.3s ease-in-out;
}

// General Info Tab
.info-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1c1e21;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f2f5;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &::before {
    content: "";
    width: 4px;
    height: 20px;
    background: linear-gradient(
      135deg,
      var(--chat-theme-color, #1877f2),
      #42b883
    );
    border-radius: 2px;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e9ecef;
    transform: translateX(4px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-weight: 600;
  color: #65676b;
  font-size: 0.9rem;
}

.info-value {
  color: #1c1e21;
  font-weight: 600;
  font-size: 0.9rem;

  &.active {
    color: #42b883;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &::before {
      content: "";
      width: 6px;
      height: 6px;
      background-color: #42b883;
      border-radius: 50%;
      display: inline-block;
    }
  }
}

// Customize Tab
.customize-section,
.background-section {
  margin-bottom: 2rem;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: transform 0.2s, border-color 0.2s;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    border-color: #ffffff;
    box-shadow: 0 0 0 2px var(--chat-theme-color, #7b42f6);
  }
}

.background-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.background-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f9fafb;
  }

  img {
    width: 100%;
    height: 60px;
    object-fit: cover;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
  }
}

.background-name {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

// Media Tab
.media-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.media-subsection {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 0.375rem;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.file-list {
  flex: 1;
  overflow-y: auto;
  max-height: 200px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }
}

.file-icon {
  width: 40px;
  height: 40px;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.file-size {
  color: #6b7280;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;

  i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// Options Tab
.options-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  border: none;
  background-color: #f8f9fa;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.danger {
    &:hover {
      background-color: #ffeaea;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
    }

    .option-icon {
      background-color: #ffeaea;
      color: #ef4444;
    }

    .option-title {
      color: #ef4444;
    }
  }
}

.option-icon {
  width: 48px;
  height: 48px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #65676b;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.option-title {
  font-weight: 600;
  color: #1c1e21;
  font-size: 0.95rem;
}

.option-description {
  color: #65676b;
  font-size: 0.8rem;
  line-height: 1.4;
}

.option-arrow {
  color: #bcc0c4;
  font-size: 0.9rem;
  transition: all 0.2s ease;

  .option-item:hover & {
    color: #65676b;
    transform: translateX(2px);
  }
}

// Modals
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;

  &.report-modal {
    max-width: 500px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #f3f4f6;

  h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }
}

.modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s, background-color 0.2s;

  &:hover {
    color: #374151;
    background-color: #f3f4f6;
  }
}

.modal-body {
  padding: 1.5rem;
}

.modal-description {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;

  &.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  &.btn-danger {
    background-color: #ef4444;
    color: white;

    &:hover {
      background-color: #dc2626;
    }

    &:disabled {
      background-color: #fca5a5;
      cursor: not-allowed;
    }
  }
}

// Notification Modal
.notification-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification-option {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: none;
  cursor: pointer;
  text-align: left;
  transition: border-color 0.2s, background-color 0.2s;

  &:hover {
    border-color: var(--chat-theme-color, #7b42f6);
    background-color: #f9fafb;
  }
}

// Report Modal
.report-reasons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.report-reason {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;

  &:hover {
    border-color: #d1d5db;
    background-color: #f9fafb;
  }

  &.selected {
    border-color: var(--chat-theme-color, #7b42f6);
    background-color: #f0eaff;
  }

  input[type="radio"] {
    display: none;
  }
}

.reason-text {
  flex: 1;
  font-size: 0.875rem;
  color: #1f2937;
}

.reason-check {
  color: var(--chat-theme-color, #7b42f6);
  opacity: 0;
  transition: opacity 0.2s;

  .report-reason.selected & {
    opacity: 1;
  }
}

.custom-reason {
  margin-top: 1rem;
}

.custom-reason-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  resize: vertical;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: var(--chat-theme-color, #7b42f6);
    box-shadow: 0 0 0 3px rgba(123, 66, 246, 0.1);
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Mobile responsive
@media (max-width: 768px) {
  :host {
    width: 100%;
    box-shadow: none;
    border-left: none;
  }

  .user-profile {
    padding: 1.5rem 1rem 1rem;
  }

  .profile-avatar {
    width: 80px;
    height: 80px;
  }

  .profile-name {
    font-size: 1.5rem;
  }

  .tab-navigation {
    margin: 0.75rem 1rem;
    padding: 3px;
  }

  .tab-button {
    padding: 0.75rem 0.375rem;

    i {
      font-size: 1rem;
    }

    span {
      font-size: 0.65rem;
    }
  }

  .tab-content {
    padding: 0 1rem 1rem;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .color-palette {
    grid-template-columns: repeat(4, 1fr);
  }

  .background-grid {
    grid-template-columns: 1fr;
  }

  .media-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .option-item {
    padding: 1rem;
  }

  .option-icon {
    width: 40px;
    height: 40px;
  }
}
